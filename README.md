# Student Attendance Mobile App

A comprehensive mobile application for managing student attendance with separate interfaces for teachers and students, plus a web-based installer for easy deployment.

## Features

### Teacher Features
- Mark student attendance
- Post events and announcements
- Create and manage reminders
- View attendance reports
- Manage student profiles

### Student Features
- View attendance records
- Browse events and announcements
- Receive notifications for reminders
- <PERSON><PERSON> and manage account
- Update profile information

## Project Structure

```
attendance_app/
├── mobile/                 # React Native mobile app
├── backend/               # Node.js/Express API server
├── web-installer/         # Web application for one-click setup
├── database/             # Database schemas and migrations
├── docs/                 # Documentation
└── scripts/              # Deployment and setup scripts
```

## Technology Stack

- **Mobile App**: React Native
- **Backend API**: Node.js with Express
- **Database**: MongoDB
- **Web Installer**: React.js
- **Authentication**: JWT tokens
- **Real-time Features**: Socket.io

## Getting Started

1. Clone the repository
2. Visit the web installer at `http://localhost:3001`
3. Click "Install & Run" to set up everything automatically

## Development Setup

See individual README files in each directory for specific setup instructions.
