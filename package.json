{"name": "student-attendance-app", "version": "1.0.0", "description": "Student attendance mobile app with teacher and student interfaces", "main": "index.js", "scripts": {"install-all": "npm run install-backend && npm run install-mobile && npm run install-web", "install-backend": "cd backend && npm install", "install-mobile": "cd mobile && npm install", "install-web": "cd web-installer && npm install", "start-backend": "cd backend && npm start", "start-web": "cd web-installer && npm start", "start-mobile": "cd mobile && npm start", "dev": "concurrently \"npm run start-backend\" \"npm run start-web\"", "setup": "node scripts/setup.js"}, "keywords": ["attendance", "mobile", "react-native", "education", "student", "teacher"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}